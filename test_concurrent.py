#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发处理功能测试脚本
验证并发优化是否正常工作
"""

import asyncio
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.concurrent_processor import create_concurrent_processor, ThreadSafeProgressTracker
from common.config import Config
from common.Logger import logger


def mock_extract_keys(content: str):
    """模拟密钥提取函数"""
    # 简单模拟：如果内容包含"key"就返回一个假密钥
    if "key" in content.lower():
        return ["AIzaSyMOCK_KEY_FOR_TESTING_1234567890123"]
    return []


def mock_validate_key(api_key: str):
    """模拟密钥验证函数"""
    # 模拟验证延迟
    time.sleep(0.1)
    
    # 简单模拟：如果密钥包含"MOCK"就返回有效
    if "MOCK" in api_key:
        return "ok"
    else:
        return "invalid"


def mock_should_skip_item(item):
    """模拟跳过检查函数"""
    # 简单模拟：不跳过任何item
    return False, "no_skip"


class MockFileManager:
    """模拟文件管理器"""
    
    def __init__(self):
        self.saved_valid_keys = []
        self.saved_rate_limited_keys = []
    
    def save_valid_keys(self, repo_name, file_path, file_url, keys):
        self.saved_valid_keys.extend(keys)
        logger.info(f"💾 Mock saved {len(keys)} valid keys from {repo_name}")
    
    def save_rate_limited_keys(self, repo_name, file_path, file_url, keys):
        self.saved_rate_limited_keys.extend(keys)
        logger.info(f"💾 Mock saved {len(keys)} rate-limited keys from {repo_name}")


def create_mock_items(count: int):
    """创建模拟的GitHub搜索结果"""
    items = []
    for i in range(count):
        item = {
            "html_url": f"https://github.com/test/repo{i}/blob/main/file{i}.py",
            "path": f"file{i}.py",
            "sha": f"mock_sha_{i}",
            "repository": {
                "full_name": f"test/repo{i}",
                "pushed_at": "2025-01-01T00:00:00Z"
            }
        }
        items.append(item)
    return items


async def test_concurrent_processing():
    """测试并发处理功能"""
    
    logger.info("🧪 开始并发处理功能测试")
    logger.info("=" * 50)
    
    # 创建测试数据
    test_items = create_mock_items(20)  # 创建20个测试项目
    mock_file_manager = MockFileManager()
    
    logger.info(f"📋 创建了 {len(test_items)} 个测试项目")
    
    # 测试串行处理
    logger.info("\n🔄 测试串行处理...")
    start_time = time.time()
    
    serial_valid = 0
    serial_rate_limited = 0
    
    for item in test_items:
        # 模拟文件内容（一半包含key，一半不包含）
        content = f"This is test content for {item['path']}"
        if int(item['path'].split('.')[0][-1]) % 2 == 0:  # 偶数文件包含key
            content += " with api key inside"
        
        keys = mock_extract_keys(content)
        for key in keys:
            result = mock_validate_key(key)
            if result == "ok":
                serial_valid += 1
            elif result == "rate_limited":
                serial_rate_limited += 1
    
    serial_time = time.time() - start_time
    logger.info(f"✅ 串行处理完成: {serial_time:.2f}秒, 找到 {serial_valid} 个有效密钥")
    
    # 测试并发处理
    logger.info("\n🚀 测试并发处理...")
    start_time = time.time()
    
    # 创建并发处理器
    processor = create_concurrent_processor(max_file_workers=5, max_validation_workers=3)
    
    # 模拟异步GitHub工具类
    class MockAsyncGitHubUtils:
        async def __aenter__(self):
            return self
        
        async def __aexit__(self, exc_type, exc_val, exc_tb):
            pass
        
        async def get_file_content_async(self, item):
            # 模拟网络延迟
            await asyncio.sleep(0.05)
            
            # 模拟文件内容
            content = f"This is test content for {item['path']}"
            if int(item['path'].split('.')[0][-1]) % 2 == 0:  # 偶数文件包含key
                content += " with api key inside"
            
            return content
    
    # 修改并发处理器以使用模拟的GitHub工具
    async def mock_process_items_concurrent():
        # 过滤items
        filtered_items = [item for item in test_items if not mock_should_skip_item(item)[0]]
        
        if not filtered_items:
            return 0, 0
        
        logger.info(f"🚀 开始并发处理 {len(filtered_items)} 个项目...")
        
        # 初始化进度跟踪器
        progress_tracker = ThreadSafeProgressTracker(len(filtered_items))
        
        # 使用模拟的异步GitHub工具
        async with MockAsyncGitHubUtils() as github_utils:
            # 创建并发任务
            semaphore = asyncio.Semaphore(5)  # 限制并发数
            tasks = [
                mock_process_single_item(item, github_utils, progress_tracker, semaphore)
                for item in filtered_items
            ]
            
            # 执行并发任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        total_valid_keys = 0
        total_rate_limited_keys = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"❌ 任务失败: {result}")
                continue
            
            if isinstance(result, tuple) and len(result) == 2:
                valid_keys, rate_limited_keys = result
                total_valid_keys += valid_keys
                total_rate_limited_keys += rate_limited_keys
        
        # 输出最终统计
        final_stats = progress_tracker.get_stats()
        logger.info(
            f"🏁 并发处理完成! "
            f"处理: {final_stats.processed_items}/{final_stats.total_items} | "
            f"时间: {final_stats.elapsed_time:.1f}s | "
            f"速度: {final_stats.items_per_second:.1f} items/s | "
            f"找到: {total_valid_keys} 有效, {total_rate_limited_keys} 限流"
        )
        
        return total_valid_keys, total_rate_limited_keys
    
    async def mock_process_single_item(item, github_utils, progress_tracker, semaphore):
        """处理单个item"""
        async with semaphore:
            try:
                # 添加随机延迟
                await asyncio.sleep(0.01)
                
                # 异步获取文件内容
                content = await github_utils.get_file_content_async(item)
                if not content:
                    progress_tracker.update(processed=1, errors=1)
                    return 0, 0
                
                # 提取密钥
                keys = mock_extract_keys(content)
                
                if not keys:
                    progress_tracker.update(processed=1)
                    return 0, 0
                
                # 验证密钥（在线程池中执行）
                valid_keys = []
                rate_limited_keys = []
                
                for key in keys:
                    # 模拟异步验证
                    await asyncio.sleep(0.01)
                    result = mock_validate_key(key)
                    if result == "ok":
                        valid_keys.append(key)
                    elif result == "rate_limited":
                        rate_limited_keys.append(key)
                
                # 保存结果
                if valid_keys:
                    mock_file_manager.save_valid_keys(
                        item["repository"]["full_name"], 
                        item["path"], 
                        item["html_url"], 
                        valid_keys
                    )
                
                if rate_limited_keys:
                    mock_file_manager.save_rate_limited_keys(
                        item["repository"]["full_name"], 
                        item["path"], 
                        item["html_url"], 
                        rate_limited_keys
                    )
                
                # 更新进度
                progress_tracker.update(
                    processed=1,
                    valid_keys=len(valid_keys),
                    rate_limited=len(rate_limited_keys)
                )
                
                return len(valid_keys), len(rate_limited_keys)
                
            except Exception as e:
                logger.error(f"❌ 处理项目出错 {item.get('html_url', 'unknown')}: {e}")
                progress_tracker.update(processed=1, errors=1)
                return 0, 0
    
    # 执行并发处理
    concurrent_valid, concurrent_rate_limited = await mock_process_items_concurrent()
    concurrent_time = time.time() - start_time
    
    # 输出对比结果
    logger.info("\n" + "=" * 50)
    logger.info("📊 性能对比结果:")
    logger.info(f"🔄 串行处理: {serial_time:.2f}秒, 找到 {serial_valid} 个有效密钥")
    logger.info(f"🚀 并发处理: {concurrent_time:.2f}秒, 找到 {concurrent_valid} 个有效密钥")
    
    if serial_time > 0:
        speedup = serial_time / concurrent_time
        logger.info(f"⚡ 加速比: {speedup:.1f}x")
    
    # 验证结果一致性
    if serial_valid == concurrent_valid:
        logger.info("✅ 结果一致性验证通过")
    else:
        logger.warning(f"⚠️ 结果不一致: 串行 {serial_valid} vs 并发 {concurrent_valid}")
    
    logger.info("🎉 并发处理功能测试完成!")


if __name__ == "__main__":
    asyncio.run(test_concurrent_processing())
