#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON><PERSON>mi King 项目测试脚本
测试项目的核心功能组件
"""

import os
import sys
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
import json

# 添加项目路径到sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的模块是否能正常导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试基础依赖
        import google.generativeai as genai
        import requests
        from dotenv import load_dotenv
        print("✅ 基础依赖导入成功")
        
        # 测试项目模块
        from common.config import Config
        from common.Logger import logger
        from utils.github_utils import GitHubUtils
        from utils.file_manager import FileManager
        print("✅ 项目模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config():
    """测试配置模块"""
    print("\n🔍 测试配置模块...")
    
    try:
        from common.config import Config
        
        # 测试配置属性
        print(f"📊 GitHub Tokens: {len(Config.GITHUB_TOKENS)} 个")
        print(f"📊 数据路径: {Config.DATA_PATH}")
        print(f"📊 查询文件: {Config.QUERIES_FILE}")
        print(f"📊 Gemini模型: {Config.HAJIMI_CHECK_MODEL}")
        print(f"📊 日期范围: {Config.DATE_RANGE_DAYS} 天")
        
        # 测试配置检查
        config_valid = Config.check()
        if config_valid:
            print("✅ 配置验证通过")
        else:
            print("⚠️ 配置验证失败，但这可能是正常的（如缺少GitHub Token）")
        
        return True
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_file_manager():
    """测试文件管理器"""
    print("\n🔍 测试文件管理器...")
    
    try:
        from utils.file_manager import FileManager
        
        # 创建临时目录进行测试
        with tempfile.TemporaryDirectory() as temp_dir:
            fm = FileManager(temp_dir)
            
            # 测试目录创建
            result = fm.check()
            print(f"📁 文件管理器检查: {'✅ 通过' if result else '❌ 失败'}")
            
            # 测试查询文件加载
            queries = fm.get_search_queries()
            print(f"📋 加载查询数量: {len(queries)}")
            
            # 测试checkpoint功能
            checkpoint = fm.load_checkpoint()
            print(f"💾 Checkpoint加载: {'✅ 成功' if checkpoint else '❌ 失败'}")
            
        return True
    except Exception as e:
        print(f"❌ 文件管理器测试失败: {e}")
        return False

def test_key_extraction():
    """测试密钥提取功能"""
    print("\n🔍 测试密钥提取功能...")
    
    try:
        # 导入密钥提取函数
        sys.path.append('app')
        from hajimi_king import extract_keys_from_content
        
        # 测试用例
        test_cases = [
            ("AIzaSyABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890", 1),  # 有效密钥
            ("This is a test AIzaSyABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890 content", 1),  # 包含有效密钥
            ("AIzaSy123", 0),  # 太短的密钥
            ("No keys here", 0),  # 无密钥
            ("AIzaSyABC123 AIzaSyDEF456GHIJKLMNOPQRSTUVWXYZ1234567890", 1),  # 多个密钥，一个有效
        ]
        
        for content, expected_count in test_cases:
            keys = extract_keys_from_content(content)
            actual_count = len(keys)
            status = "✅" if actual_count == expected_count else "❌"
            print(f"{status} 测试用例: 期望{expected_count}个密钥，实际{actual_count}个")
        
        return True
    except Exception as e:
        print(f"❌ 密钥提取测试失败: {e}")
        return False

def test_query_normalization():
    """测试查询规范化功能"""
    print("\n🔍 测试查询规范化功能...")
    
    try:
        sys.path.append('app')
        from hajimi_king import normalize_query
        
        # 测试用例
        test_cases = [
            ("AIzaSy in:file", "AIzaSy in:file"),
            ("  AIzaSy   in:file  ", "AIzaSy in:file"),
            ('"exact match" language:python', '"exact match" language:python'),
            ("path:config filename:env language:shell", "filename:env language:shell path:config"),
        ]
        
        for input_query, expected in test_cases:
            result = normalize_query(input_query)
            status = "✅" if result == expected else "❌"
            print(f"{status} 查询规范化: '{input_query}' -> '{result}'")
        
        return True
    except Exception as e:
        print(f"❌ 查询规范化测试失败: {e}")
        return False

def test_github_utils_mock():
    """测试GitHub工具类（使用模拟数据）"""
    print("\n🔍 测试GitHub工具类（模拟）...")
    
    try:
        from utils.github_utils import GitHubUtils
        
        # 创建模拟的GitHub工具实例
        mock_tokens = ["fake_token_1", "fake_token_2"]
        github_utils = GitHubUtils.create_instance(mock_tokens)
        
        print(f"🔧 GitHub工具实例创建: {'✅ 成功' if github_utils else '❌ 失败'}")
        print(f"🎫 Token数量: {len(mock_tokens)}")
        
        return True
    except Exception as e:
        print(f"❌ GitHub工具类测试失败: {e}")
        return False

def test_project_structure():
    """测试项目结构"""
    print("\n🔍 测试项目结构...")
    
    required_files = [
        "app/hajimi_king.py",
        "common/config.py",
        "common/Logger.py",
        "utils/github_utils.py",
        "utils/file_manager.py",
        "pyproject.toml",
        ".env",
        "queries.txt",
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ 所有必要文件都存在")
        return True

def run_basic_functionality_test():
    """运行基本功能测试（不需要真实API调用）"""
    print("\n🔍 运行基本功能测试...")
    
    try:
        # 模拟运行主程序的初始化部分
        sys.path.append('app')
        from hajimi_king import main
        from common.config import Config
        from utils.file_manager import FileManager
        
        # 检查配置
        print("📋 检查配置...")
        config_ok = Config.check()
        print(f"配置状态: {'✅ 正常' if config_ok else '⚠️ 需要配置GitHub Token'}")
        
        # 检查文件管理器
        print("📁 检查文件管理器...")
        file_manager = FileManager(Config.DATA_PATH)
        fm_ok = file_manager.check()
        print(f"文件管理器状态: {'✅ 正常' if fm_ok else '❌ 异常'}")
        
        # 加载查询
        print("📋 加载搜索查询...")
        queries = file_manager.get_search_queries()
        print(f"查询数量: {len(queries)}")
        if queries:
            print(f"示例查询: {queries[0]}")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎪 Hajimi King 项目测试开始")
    print("=" * 50)
    
    test_results = []
    
    # 运行各项测试
    tests = [
        ("模块导入", test_imports),
        ("项目结构", test_project_structure),
        ("配置模块", test_config),
        ("文件管理器", test_file_manager),
        ("密钥提取", test_key_extraction),
        ("查询规范化", test_query_normalization),
        ("GitHub工具类", test_github_utils_mock),
        ("基本功能", run_basic_functionality_test),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            test_results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！项目可以正常运行。")
        print("\n💡 下一步:")
        print("  1. 确保.env文件中的GitHub Token有效")
        print("  2. 运行: python app/hajimi_king.py")
    else:
        print("⚠️ 部分测试失败，请检查相关问题。")
    
    return passed == total

if __name__ == "__main__":
    main()
