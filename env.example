# GitHub API tokens配置 (逗号分隔)
# 在 https://github.com/settings/tokens 创建token

GITHUB_TOKENS=
HAJIMI_CHECK_MODEL=gemini-2.5-flash
# 文件相关
DATA_PATH=./data
# 有效密钥文件前缀
VALID_KEY_DETAIL_PREFIX=logs/keys_valid_detail_
VALID_KEY_PREFIX=keys/keys_valid_
# 429错误(频率限制)密钥文件前缀
RATE_LIMITED_KEY_PREFIX=keys/key_429_
RATE_LIMITED_KEY_DETAIL_PREFIX=logs/key_429_detail
# 日期范围过滤器配置 (单位：天，过滤掉超过指定天数的旧仓库)
# 默认730天 (约2年)，可根据需要调整
DATE_RANGE_DAYS=730
# 搜索查询文件路径 (相对于项目根目录)
# 默认为项目根目录下的queries.txt文件
QUERIES_FILE=queries.txt
# 已扫描SHA文件路径 (相对于data目录)
# 用于记录已扫描过的文件SHA，避免重复扫描
SCANNED_SHAS_FILE=scanned_shas.txt
# 文件路径黑名单配置 (逗号分隔，用于跳过文档和示例文件)
# 支持文件名关键词、路径片段、文件扩展名等
FILE_PATH_BLACKLIST=readme,docs,doc/,.md,example,sample,tutorial,test,spec,demo,mock
