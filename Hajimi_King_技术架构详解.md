# 🎪 Hajimi King 技术架构详解

## 📋 目录
- [项目概述](#项目概述)
- [整体架构](#整体架构)
- [核心组件详解](#核心组件详解)
- [关键算法分析](#关键算法分析)
- [数据流分析](#数据流分析)
- [性能优化策略](#性能优化策略)
- [安全机制](#安全机制)
- [部署架构](#部署架构)

---

## 🎯 项目概述

### 核心功能
Hajimi King是一个**智能化的API密钥发现和验证系统**，专门用于：
- 在GitHub上大规模搜索Google Gemini API密钥
- 实时验证密钥的有效性
- 智能过滤和去重处理
- 提供完整的审计日志和结果管理

### 技术特点
- **高效搜索**：利用GitHub搜索API，支持复杂查询语法
- **智能验证**：直接调用Google Generative AI API验证
- **增量处理**：断点续传，避免重复工作
- **生产就绪**：完整的错误处理、监控和部署方案

---

## 🏗️ 整体架构

### 架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Hajimi King 系统架构                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   配置管理   │    │   日志系统   │    │   文件管理   │     │
│  │  Config     │    │   Logger    │    │FileManager  │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
├─────────────────────────────────────────────────────────────┤
│                      主控制器                                │
│                  hajimi_king.py                             │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │ GitHub搜索  │    │ 密钥提取    │    │ 密钥验证    │     │
│  │GitHubUtils  │    │extract_keys │    │validate_key │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
├─────────────────────────────────────────────────────────────┤
│                      外部API                                │
│  ┌─────────────┐              ┌─────────────┐              │
│  │ GitHub API  │              │ Google AI   │              │
│  │   搜索接口   │              │   验证接口   │              │
│  └─────────────┘              └─────────────┘              │
└─────────────────────────────────────────────────────────────┘
```

### 模块化设计
项目采用清晰的模块化架构：

```
hajimi-king/
├── app/                    # 主应用程序
│   └── hajimi_king.py     # 核心控制逻辑
├── common/                # 公共模块
│   ├── config.py          # 配置管理
│   └── Logger.py          # 日志系统
├── utils/                 # 工具模块
│   ├── github_utils.py    # GitHub API交互
│   └── file_manager.py    # 文件和数据管理
└── data/                  # 数据存储
    ├── checkpoint.json    # 检查点数据
    ├── queries.txt        # 搜索查询配置
    └── keys/              # 密钥结果文件
```

---

## 🔧 核心组件详解

### 1. 配置管理系统 (Config)

**设计理念**：集中化配置管理，支持环境变量和默认值

<augment_code_snippet path="common/config.py" mode="EXCERPT">
````python
class Config:
    # GitHub tokens列表，支持多token轮换
    GITHUB_TOKENS = [token.strip() for token in GITHUB_TOKENS_STR.split(',') if token.strip()]
    
    # 核心配置参数
    DATA_PATH = os.getenv('DATA_PATH', 'data')
    DATE_RANGE_DAYS = int(os.getenv("DATE_RANGE_DAYS", "730"))
    HAJIMI_CHECK_MODEL = os.getenv("HAJIMI_CHECK_MODEL", "gemini-2.5-flash")
    
    @classmethod
    def check(cls) -> bool:
        """完整的配置验证逻辑"""
        # 验证必要配置项...
````
</augment_code_snippet>

**核心特性**：
- **环境变量支持**：所有配置都可通过环境变量覆盖
- **配置验证**：启动时自动验证配置完整性
- **代理支持**：内置HTTP代理配置
- **灵活的文件路径**：支持自定义数据目录和文件前缀

### 2. GitHub搜索引擎 (GitHubUtils)

**设计理念**：高效、稳定的GitHub API交互，支持大规模搜索

<augment_code_snippet path="utils/github_utils.py" mode="EXCERPT">
````python
class GitHubUtils:
    def __init__(self, tokens: List[str]):
        self.tokens = [token.strip() for token in tokens if token.strip()]
        self._token_ptr = 0  # Token轮换指针
    
    def search_for_keys(self, query: str, max_retries: int = 5):
        # 分页搜索，最多10页，每页100个结果
        for page in range(1, 11):
            # 指数退避重试机制
            for attempt in range(1, max_retries + 1):
                current_token = self._next_token()
                # API调用和错误处理...
````
</augment_code_snippet>

**核心技术**：
- **Token轮换**：自动轮换多个GitHub Token，提高API配额
- **分页搜索**：支持最多1000个结果（10页×100个/页）
- **指数退避**：智能重试机制，处理速率限制
- **错误分类**：区分网络错误、HTTP错误、API错误

### 3. 文件管理系统 (FileManager)

**设计理念**：高效的数据持久化和状态管理

<augment_code_snippet path="utils/file_manager.py" mode="EXCERPT">
````python
@dataclass
class Checkpoint:
    last_scan_time: Optional[str] = None
    scanned_shas: Set[str] = field(default_factory=set)
    processed_queries: Set[str] = field(default_factory=set)
    
class FileManager:
    def __init__(self, data_dir: str):
        # 动态文件名，按时间自动分割
        self._keys_valid_filename = os.path.join(
            self.data_dir, 
            f"{Config.VALID_KEY_PREFIX}{start_time.strftime('%Y%m%d_%H')}.txt"
        )
````
</augment_code_snippet>

**核心特性**：
- **Checkpoint机制**：支持断点续传，程序重启后继续处理
- **SHA去重**：记录已处理文件的SHA值，避免重复处理
- **动态文件名**：按时间自动分割输出文件（按小时/按天）
- **分离存储**：大量SHA数据与配置数据分开存储，提高性能

### 4. 密钥提取引擎

**设计理念**：精确的模式匹配和智能过滤

<augment_code_snippet path="app/hajimi_king.py" mode="EXCERPT">
````python
def extract_keys_from_content(content: str) -> List[str]:
    # Google API密钥的精确正则表达式
    pattern = r'(AIzaSy[A-Za-z0-9\-_]{33})'
    return re.findall(pattern, content)

def process_item(item: Dict[str, Any]) -> tuple:
    # 过滤占位符密钥
    filtered_keys = []
    for key in keys:
        context_index = content.find(key)
        if context_index != -1:
            snippet = content[context_index:context_index + 45]
            if "..." in snippet or "YOUR_" in snippet.upper():
                continue  # 跳过占位符
        filtered_keys.append(key)
````
</augment_code_snippet>

**技术细节**：
- **精确匹配**：`AIzaSy[A-Za-z0-9\-_]{33}` 匹配Google API密钥格式
- **上下文分析**：检查密钥周围文本，过滤占位符和示例代码
- **智能过滤**：识别"..."、"YOUR_"等常见占位符模式

---

## 🧠 关键算法分析

### 1. 密钥验证算法

<augment_code_snippet path="app/hajimi_king.py" mode="EXCERPT">
````python
def validate_gemini_key(api_key: str) -> Union[bool, str]:
    try:
        time.sleep(random.uniform(0.5, 1.5))  # 随机延迟
        
        genai.configure(
            api_key=api_key,
            transport="rest",
            client_options={"api_endpoint": "generativelanguage.googleapis.com"},
        )
        
        model = genai.GenerativeModel(Config.HAJIMI_CHECK_MODEL)
        response = model.generate_content("hi")  # 简单测试请求
        return "ok"
    except google_exceptions.TooManyRequests:
        return "rate_limited"
    except Exception as e:
        # 详细的错误分类逻辑...
````
</augment_code_snippet>

**算法特点**：
- **真实验证**：直接调用Google API，确保密钥可用
- **错误分类**：区分无效、速率限制、服务禁用等状态
- **随机延迟**：避免过于频繁的API调用
- **轻量测试**：使用简单的"hi"请求，最小化API消耗

### 2. 增量扫描算法

<augment_code_snippet path="app/hajimi_king.py" mode="EXCERPT">
````python
def should_skip_item(item: Dict[str, Any], checkpoint: Checkpoint) -> tuple[bool, str]:
    # 1. 时间过滤：基于仓库最后推送时间
    if checkpoint.last_scan_time:
        last_scan_dt = datetime.fromisoformat(checkpoint.last_scan_time)
        repo_pushed_dt = datetime.strptime(repo_pushed_at, "%Y-%m-%dT%H:%M:%SZ")
        if repo_pushed_dt <= last_scan_dt:
            return True, "time_filter"
    
    # 2. SHA去重：避免重复处理相同文件
    if item.get("sha") in checkpoint.scanned_shas:
        return True, "sha_duplicate"
    
    # 3. 年龄过滤：跳过过旧的仓库
    if repo_pushed_dt < datetime.utcnow() - timedelta(days=Config.DATE_RANGE_DAYS):
        return True, "age_filter"
    
    # 4. 路径黑名单：跳过文档和示例文件
    if any(token in lowercase_path for token in Config.FILE_PATH_BLACKLIST):
        return True, "doc_filter"
````
</augment_code_snippet>

**多层过滤机制**：
1. **时间过滤**：只处理上次扫描后更新的仓库
2. **SHA去重**：基于文件内容哈希避免重复
3. **年龄过滤**：跳过超过730天的旧仓库
4. **路径过滤**：跳过readme、docs、example等文件

### 3. 查询规范化算法

<augment_code_snippet path="app/hajimi_king.py" mode="EXCERPT">
````python
def normalize_query(query: str) -> str:
    # 解析查询字符串，处理引号、语言、文件名等参数
    parts = []
    # 复杂的字符串解析逻辑...
    
    # 按类型分组和排序
    normalized_parts = []
    normalized_parts.extend(sorted(quoted_strings))
    normalized_parts.extend(sorted(other_parts))
    normalized_parts.extend(sorted(language_parts))
    normalized_parts.extend(sorted(filename_parts))
    normalized_parts.extend(sorted(path_parts))
    
    return " ".join(normalized_parts)
````
</augment_code_snippet>

**规范化目的**：
- **去重检测**：相同语义的查询只执行一次
- **参数排序**：确保查询的一致性
- **引号处理**：正确处理GitHub搜索语法中的引号

---

## 📊 数据流分析

### 完整数据流程

```
1. 启动阶段
   ├── 加载配置 (Config.check())
   ├── 初始化文件管理器 (FileManager)
   ├── 加载Checkpoint (断点续传)
   └── 加载搜索查询 (queries.txt)

2. 搜索阶段
   ├── 遍历查询列表
   ├── GitHub API搜索 (GitHubUtils.search_for_keys)
   ├── 分页获取结果 (最多1000个)
   └── 应用过滤规则 (should_skip_item)

3. 处理阶段
   ├── 获取文件内容 (GitHubUtils.get_file_content)
   ├── 提取密钥 (extract_keys_from_content)
   ├── 过滤占位符 (上下文分析)
   └── 验证密钥 (validate_gemini_key)

4. 存储阶段
   ├── 保存有效密钥 (FileManager.save_valid_keys)
   ├── 保存速率限制密钥 (FileManager.save_rate_limited_keys)
   ├── 更新Checkpoint (FileManager.save_checkpoint)
   └── 记录统计信息

5. 循环执行
   ├── 每20个文件保存一次进度
   ├── 每5个查询休息1秒
   ├── 每轮循环间隔10秒
   └── 支持Ctrl+C优雅退出
```

### 数据存储结构

```
data/
├── checkpoint.json              # 检查点数据
│   ├── last_scan_time          # 最后扫描时间
│   └── processed_queries       # 已处理查询列表
├── scanned_shas.txt            # 已扫描文件SHA列表
├── queries.txt                 # 搜索查询配置
├── keys/                       # 密钥结果文件
│   ├── keys_valid_20250725_17.txt      # 有效密钥
│   └── key_429_20250725_17.txt         # 速率限制密钥
└── logs/                       # 详细日志
    ├── keys_valid_detail_20250725.log  # 有效密钥详细信息
    └── key_429_detail20250725.log      # 速率限制详细信息
```

---

## ⚡ 性能优化策略

### 1. API调用优化
- **Token轮换**：多个GitHub Token轮换使用，提高API配额
- **智能重试**：指数退避算法，避免无效重试
- **批量处理**：每20个文件批量保存进度
- **随机延迟**：避免触发API速率限制

### 2. 内存管理
- **流式处理**：逐个处理搜索结果，避免大量数据驻留内存
- **分离存储**：大量SHA数据单独存储，减少JSON解析开销
- **动态文件名**：按时间分割文件，避免单文件过大

### 3. I/O优化
- **追加写入**：使用追加模式写入文件，提高I/O效率
- **批量保存**：定期批量保存Checkpoint，减少磁盘写入
- **目录预创建**：启动时创建所有必要目录和文件

### 4. 网络优化
- **连接复用**：requests库自动处理连接池
- **代理支持**：支持HTTP代理，适应不同网络环境
- **超时控制**：30秒超时，避免长时间等待

---

## 🔒 安全机制

### 1. API安全
- **最小权限**：GitHub Token只需要`public_repo`读取权限
- **Token轮换**：支持多Token配置，降低单点风险
- **速率限制**：智能处理API限制，避免账号封禁

### 2. 数据安全
- **敏感信息过滤**：自动过滤占位符和示例密钥
- **本地存储**：所有数据本地存储，不上传到外部服务
- **访问控制**：文件权限控制，防止未授权访问

### 3. 运行安全
- **异常处理**：完整的异常捕获和恢复机制
- **优雅退出**：支持Ctrl+C优雅中断，保存当前进度
- **错误隔离**：单个文件处理失败不影响整体流程

---

## 🐳 部署架构

### Docker化部署

<augment_code_snippet path="Dockerfile" mode="EXCERPT">
````dockerfile
FROM registry-1.docker.io/library/python:3.11-slim

# 设置工作目录和环境变量
WORKDIR /app
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 安装依赖
RUN pip install uv
RUN uv pip install --system --no-cache -r pyproject.toml

# 启动命令
CMD ["python", "app/hajimi_king.py"]
````
</augment_code_snippet>

### 一键部署脚本

<augment_code_snippet path="first_deploy.sh" mode="EXCERPT">
````bash
# 自动化部署流程
main() {
    print_banner
    check_source_directory      # 检查源码完整性
    check_docker               # 验证Docker环境
    setup_deploy_directory     # 创建部署目录
    configure_github_token     # 交互式配置Token
    cleanup_existing          # 清理旧容器
    build_image               # 构建Docker镜像
    start_services            # 启动服务
}
````
</augment_code_snippet>

### 生产环境考虑
- **数据持久化**：Docker卷挂载确保数据不丢失
- **自动重启**：`restart: unless-stopped`策略
- **日志管理**：`docker-compose logs -f`查看实时日志
- **资源限制**：可配置内存和CPU限制

---

## 🎯 总结

Hajimi King项目展现了现代Python应用的最佳实践：

### 技术亮点
1. **模块化架构**：清晰的职责分离和接口设计
2. **生产就绪**：完整的错误处理、日志记录、部署方案
3. **性能优化**：多层缓存、批量处理、智能重试
4. **安全考虑**：最小权限、数据过滤、异常隔离

### 学习价值
- **API集成**：GitHub API和Google AI API的实际应用
- **并发处理**：Token轮换和速率限制处理
- **数据管理**：增量处理和状态持久化
- **DevOps实践**：Docker化部署和自动化脚本

这个项目是学习现代Python开发、API集成、数据处理和DevOps实践的优秀案例！🚀
