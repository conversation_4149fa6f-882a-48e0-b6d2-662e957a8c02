import os
import random
import re
import sys
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Union, Any

import google.generativeai as genai
from google.api_core import exceptions as google_exceptions

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from common.Logger import logger
from common.config import Config
from utils.github_utils import GitHubUtils
from utils.file_manager import FileManager, Checkpoint
from utils.concurrent_processor import create_concurrent_processor

# 创建GitHub工具实例和文件管理器
github_utils = GitHubUtils.create_instance(Config.GITHUB_TOKENS)
file_manager = FileManager(Config.DATA_PATH)

# 统计信息
skip_stats = {
    "time_filter": 0,
    "sha_duplicate": 0,
    "age_filter": 0,
    "doc_filter": 0
}


def normalize_query(query: str) -> str:
    query = " ".join(query.split())

    parts = []
    i = 0
    while i < len(query):
        if query[i] == '"':
            end_quote = query.find('"', i + 1)
            if end_quote != -1:
                parts.append(query[i:end_quote + 1])
                i = end_quote + 1
            else:
                parts.append(query[i])
                i += 1
        elif query[i] == ' ':
            i += 1
        else:
            start = i
            while i < len(query) and query[i] != ' ':
                i += 1
            parts.append(query[start:i])

    quoted_strings = []
    language_parts = []
    filename_parts = []
    path_parts = []
    other_parts = []

    for part in parts:
        if part.startswith('"') and part.endswith('"'):
            quoted_strings.append(part)
        elif part.startswith('language:'):
            language_parts.append(part)
        elif part.startswith('filename:'):
            filename_parts.append(part)
        elif part.startswith('path:'):
            path_parts.append(part)
        elif part.strip():
            other_parts.append(part)

    normalized_parts = []
    normalized_parts.extend(sorted(quoted_strings))
    normalized_parts.extend(sorted(other_parts))
    normalized_parts.extend(sorted(language_parts))
    normalized_parts.extend(sorted(filename_parts))
    normalized_parts.extend(sorted(path_parts))

    return " ".join(normalized_parts)


def extract_keys_from_content(content: str) -> List[str]:
    pattern = r'(AIzaSy[A-Za-z0-9\-_]{33})'
    return re.findall(pattern, content)


def should_skip_item(item: Dict[str, Any], checkpoint: Checkpoint) -> tuple[bool, str]:
    """
    检查是否应该跳过处理此item
    
    Returns:
        tuple: (should_skip, reason)
    """
    # 检查增量扫描时间
    if checkpoint.last_scan_time:
        try:
            last_scan_dt = datetime.fromisoformat(checkpoint.last_scan_time)
            repo_pushed_at = item["repository"].get("pushed_at")
            if repo_pushed_at:
                repo_pushed_dt = datetime.strptime(repo_pushed_at, "%Y-%m-%dT%H:%M:%SZ")
                if repo_pushed_dt <= last_scan_dt:
                    skip_stats["time_filter"] += 1
                    return True, "time_filter"
        except Exception as e:
            pass

    # 检查SHA是否已扫描
    if item.get("sha") in checkpoint.scanned_shas:
        skip_stats["sha_duplicate"] += 1
        return True, "sha_duplicate"

    # 检查仓库年龄
    repo_pushed_at = item["repository"].get("pushed_at")
    if repo_pushed_at:
        repo_pushed_dt = datetime.strptime(repo_pushed_at, "%Y-%m-%dT%H:%M:%SZ")
        if repo_pushed_dt < datetime.utcnow() - timedelta(days=Config.DATE_RANGE_DAYS):
            skip_stats["age_filter"] += 1
            return True, "age_filter"

    # 检查文档和示例文件
    lowercase_path = item["path"].lower()
    if any(token in lowercase_path for token in Config.FILE_PATH_BLACKLIST):
        skip_stats["doc_filter"] += 1
        return True, "doc_filter"

    return False, ""


async def process_items_batch_concurrent(items: List[Dict[str, Any]], checkpoint: Checkpoint) -> tuple:
    """
    并发处理一批GitHub搜索结果items

    Args:
        items: GitHub搜索结果列表
        checkpoint: 检查点对象

    Returns:
        tuple: (valid_keys_count, rate_limited_keys_count)
    """
    if not items:
        return 0, 0

    # 创建并发处理器
    processor = create_concurrent_processor(
        max_file_workers=Config.MAX_FILE_WORKERS,
        max_validation_workers=Config.MAX_VALIDATION_WORKERS
    )

    # 执行并发处理
    valid_keys_count, rate_limited_keys_count = await processor.process_items_concurrent(
        items=items,
        extract_keys_func=extract_keys_from_content,
        validate_key_func=validate_gemini_key,
        file_manager=file_manager,
        should_skip_func=lambda item: should_skip_item(item, checkpoint)
    )

    return valid_keys_count, rate_limited_keys_count


def process_item(item: Dict[str, Any]) -> tuple:
    """
    处理单个GitHub搜索结果item
    
    Returns:
        tuple: (valid_keys_count, rate_limited_keys_count)
    """
    delay = random.uniform(1, 4)
    file_url = item["html_url"]

    # 简化日志输出，只显示关键信息
    repo_name = item["repository"]["full_name"]
    file_path = item["path"]
    time.sleep(delay)

    content = github_utils.get_file_content(item)
    if not content:
        logger.warning(f"⚠️ Failed to fetch content for file: {file_url}")
        return 0, 0

    keys = extract_keys_from_content(content)

    # 过滤占位符密钥
    filtered_keys = []
    for key in keys:
        context_index = content.find(key)
        if context_index != -1:
            snippet = content[context_index:context_index + 45]
            if "..." in snippet or "YOUR_" in snippet.upper():
                continue
        filtered_keys.append(key)
    keys = filtered_keys

    if not keys:
        return 0, 0

    logger.info(f"🔑 Found {len(keys)} suspected key(s), validating...")

    valid_keys = []
    rate_limited_keys = []

    # 验证每个密钥
    for key in keys:
        validation_result = validate_gemini_key(key)
        if "ok" in validation_result:
            valid_keys.append(key)
            logger.info(f"✅ VALID: {key}")
        elif validation_result == "rate_limited":
            rate_limited_keys.append(key)
            logger.warning(f"⚠️ RATE LIMITED: {key}, check result: {validation_result}")
        else:
            logger.info(f"❌ INVALID: {key}, check result: {validation_result}")

    # 保存结果
    if valid_keys:
        file_manager.save_valid_keys(repo_name, file_path, file_url, valid_keys)
        logger.info(f"💾 Saved {len(valid_keys)} valid key(s)")

    if rate_limited_keys:
        file_manager.save_rate_limited_keys(repo_name, file_path, file_url, rate_limited_keys)
        logger.info(f"💾 Saved {len(rate_limited_keys)} rate limited key(s)")

    return len(valid_keys), len(rate_limited_keys)


def validate_gemini_key(api_key: str) -> Union[bool, str]:
    try:
        time.sleep(random.uniform(0.5, 1.5))

        genai.configure(
            api_key=api_key,
            transport="rest",
            client_options={"api_endpoint": "generativelanguage.googleapis.com"},
        )

        model = genai.GenerativeModel(Config.HAJIMI_CHECK_MODEL)
        response = model.generate_content("hi")
        return "ok"
    except (google_exceptions.PermissionDenied, google_exceptions.Unauthenticated) as e:
        return False
    except google_exceptions.TooManyRequests as e:
        return "rate_limited"
    except Exception as e:
        if "429" in str(e) or "rate limit" in str(e).lower() or "quota" in str(e).lower():
            return "rate_limited:429"
        elif "403" in str(e) or "SERVICE_DISABLED" in str(e) or "API has not been used" in str(e):
            return "disabled"
        else:
            return "error"


def print_skip_stats():
    """打印跳过统计信息"""
    total_skipped = sum(skip_stats.values())
    if total_skipped > 0:
        logger.info(f"📊 Skipped {total_skipped} items - Time: {skip_stats['time_filter']}, Duplicate: {skip_stats['sha_duplicate']}, Age: {skip_stats['age_filter']}, Docs: {skip_stats['doc_filter']}")


def reset_skip_stats():
    """重置跳过统计"""
    global skip_stats
    skip_stats = {"time_filter": 0, "sha_duplicate": 0, "age_filter": 0, "doc_filter": 0}


async def main():
    start_time = datetime.now()

    # 打印系统启动信息
    logger.info("=" * 60)
    logger.info("🚀 HAJIMI KING STARTING")
    logger.info("=" * 60)
    logger.info(f"⏰ Started at: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

    # 1. 检查配置
    if not Config.check():
        logger.error("❌ Configuration check failed. Exiting...")
        logger.info("You can create GitHub tokens at: https://github.com/settings/tokens")
        sys.exit(1)
    # 2. 检查文件管理器
    if not file_manager.check():
        logger.error("❌ FileManager check failed. Exiting...")
        sys.exit(1)

    # 3. 显示系统信息
    search_queries = file_manager.get_search_queries()
    logger.info("📋 SYSTEM INFORMATION:")
    logger.info(f"🔑 GitHub tokens: {len(Config.GITHUB_TOKENS)} configured")
    logger.info(f"🔍 Search queries: {len(search_queries)} loaded")
    logger.info(f"📅 Date filter: {Config.DATE_RANGE_DAYS} days")
    if Config.PROXY:
        logger.info(f"🌐 Proxy: {Config.PROXY}")

    # 显示并发配置
    if Config.ENABLE_CONCURRENT_PROCESSING:
        logger.info(f"🚀 Concurrent processing: ENABLED")
        logger.info(f"   File workers: {Config.MAX_FILE_WORKERS}")
        logger.info(f"   Validation workers: {Config.MAX_VALIDATION_WORKERS}")
        logger.info(f"   Batch size: {Config.CONCURRENT_BATCH_SIZE}")
    else:
        logger.info(f"🔄 Concurrent processing: DISABLED (using serial mode)")

    # 4. 加载checkpoint并显示状态
    checkpoint = file_manager.load_checkpoint()
    if checkpoint.last_scan_time:
        logger.info(f"💾 Checkpoint found - Incremental scan mode")
        logger.info(f"   Last scan: {checkpoint.last_scan_time}")
        logger.info(f"   Scanned files: {len(checkpoint.scanned_shas)}")
        logger.info(f"   Processed queries: {len(checkpoint.processed_queries)}")
    else:
        logger.info(f"💾 No checkpoint - Full scan mode")


    logger.info("✅ System ready - Starting king")
    logger.info("=" * 60)

    total_keys_found = 0
    total_rate_limited_keys = 0
    loop_count = 0

    while True:
        try:
            loop_count += 1
            logger.info(f"🔄 Loop #{loop_count} - {datetime.now().strftime('%H:%M:%S')}")

            query_count = 0
            loop_processed_files = 0
            reset_skip_stats()

            for i, q in enumerate(search_queries, 1):
                normalized_q = normalize_query(q)
                if normalized_q in checkpoint.processed_queries:
                    logger.info(f"🔍 Skipping already processed query: [{q}],index:#{i}")
                    continue

                res = github_utils.search_for_keys(q)

                if res and "items" in res:
                    items = res["items"]
                    if items:
                        logger.info(f"🔍 Query {i}/{len(search_queries)}: [{q}] - Found {len(items)} items")

                        # 选择处理模式：并发或串行
                        if Config.ENABLE_CONCURRENT_PROCESSING and len(items) > 1:
                            # 并发处理模式
                            logger.info(f"🚀 Using concurrent processing for {len(items)} items...")

                            # 分批处理以避免内存过载
                            batch_size = Config.CONCURRENT_BATCH_SIZE
                            query_valid_keys = 0
                            query_rate_limited_keys = 0

                            for batch_start in range(0, len(items), batch_size):
                                batch_end = min(batch_start + batch_size, len(items))
                                batch_items = items[batch_start:batch_end]

                                logger.info(f"📦 Processing batch {batch_start//batch_size + 1}: items {batch_start+1}-{batch_end}")

                                # 异步并发处理批次
                                batch_valid, batch_rate_limited = await process_items_batch_concurrent(batch_items, checkpoint)

                                query_valid_keys += batch_valid
                                query_rate_limited_keys += batch_rate_limited

                                # 记录已扫描的SHA
                                for item in batch_items:
                                    checkpoint.add_scanned_sha(item.get("sha"))

                                # 保存进度
                                file_manager.save_checkpoint(checkpoint)
                                file_manager.update_dynamic_filenames()

                            query_processed = len(items)
                            loop_processed_files += len(items)

                        else:
                            # 串行处理模式（原有逻辑）
                            logger.info(f"🔄 Using serial processing for {len(items)} items...")
                            query_valid_keys = 0
                            query_rate_limited_keys = 0
                            query_processed = 0

                            for item_index, item in enumerate(items, 1):
                                # 每20个item保存checkpoint并显示进度
                                if item_index % 20 == 0:
                                    logger.info(
                                        f"📈 Progress: {item_index}/{len(items)} | query: {q} | current valid: {query_valid_keys} | current rate limited: {query_rate_limited_keys} | total valid: {total_keys_found} | total rate limited: {total_rate_limited_keys}")
                                    file_manager.save_checkpoint(checkpoint)
                                    file_manager.update_dynamic_filenames()

                                # 检查是否应该跳过此item
                                should_skip, skip_reason = should_skip_item(item, checkpoint)
                                if should_skip:
                                    logger.info(f"🚫 Skipping item,name: {item.get('path','').lower()},index:{item_index} - reason: {skip_reason}")
                                    continue

                                # 处理单个item
                                valid_count, rate_limited_count = process_item(item)

                                query_valid_keys += valid_count
                                query_rate_limited_keys += rate_limited_count
                                query_processed += 1

                                # 记录已扫描的SHA
                                checkpoint.add_scanned_sha(item.get("sha"))

                                loop_processed_files += 1



                        total_keys_found += query_valid_keys
                        total_rate_limited_keys += query_rate_limited_keys

                        if query_processed > 0:
                            logger.info(f"✅ Query {i}/{len(search_queries)} complete - Processed: {query_processed}, Valid: +{query_valid_keys}, Rate limited: +{query_rate_limited_keys}")
                        else:
                            logger.info(f"⏭️ Query {i}/{len(search_queries)} complete - All items skipped")

                        print_skip_stats()
                    else:
                        logger.info(f"📭 Query {i}/{len(search_queries)} - No items found")
                else:
                    logger.warning(f"❌ Query {i}/{len(search_queries)} failed")

                checkpoint.add_processed_query(normalized_q)
                query_count += 1

                checkpoint.update_scan_time()
                file_manager.save_checkpoint(checkpoint)
                file_manager.update_dynamic_filenames()

                if query_count % 5 == 0:
                    logger.info(f"⏸️ Processed {query_count} queries, taking a break...")
                    time.sleep(1)

            logger.info(f"🏁 Loop #{loop_count} complete - Processed {loop_processed_files} files | Total valid: {total_keys_found} | Total rate limited: {total_rate_limited_keys}")

            logger.info(f"💤 Sleeping for 10 seconds...")
            time.sleep(10)

        except KeyboardInterrupt:
            logger.info("⛔ Interrupted by user")
            checkpoint.update_scan_time()
            file_manager.save_checkpoint(checkpoint)
            logger.info(f"📊 Final stats - Valid keys: {total_keys_found}, Rate limited: {total_rate_limited_keys}")
            break
        except Exception as e:
            logger.error(f"💥 Unexpected error: {e}")
            logger.info("🔄 Continuing...")
            continue


if __name__ == "__main__":
    # 运行异步主函数
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("⛔ Program interrupted by user")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)
