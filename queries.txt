# GitHub搜索查询配置文件 - 优化版
# 每行一个查询语句，支持GitHub搜索语法
# 以#开头的行为注释，空行会被忽略
#
# 🎯 查询策略说明：
# 1. 基础搜索：直接搜索API密钥模式
# 2. 文件类型搜索：针对特定文件类型
# 3. 上下文搜索：结合常见使用场景
# 4. 项目类型搜索：针对特定项目类型
# 5. 配置文件搜索：重点关注配置文件
# 6. 时间优化搜索：优先最近更新的项目

# ==========================================
# 🔥 高优先级查询 - 基础搜索
# ==========================================

# 基础API密钥搜索
AIzaSy in:file

# 带引号的精确搜索（避免部分匹配）
"AIzaSy" in:file

# ==========================================
# 📁 文件类型定向搜索
# ==========================================

# Python项目中的API密钥
AIzaSy extension:py

# JavaScript/Node.js项目
AIzaSy extension:js
AIzaSy extension:ts
AIzaSy extension:jsx
AIzaSy extension:tsx

# 配置文件中的密钥
AIzaSy extension:json
AIzaSy extension:yaml
AIzaSy extension:yml
AIzaSy extension:toml
AIzaSy extension:ini
AIzaSy extension:cfg
AIzaSy extension:conf

# 环境配置文件
AIzaSy extension:env
AIzaSy filename:.env
AIzaSy filename:config

# Shell脚本和批处理文件
AIzaSy extension:sh
AIzaSy extension:bash
AIzaSy extension:bat
AIzaSy extension:cmd

# 文档和说明文件（可能包含示例密钥）
AIzaSy extension:md
AIzaSy extension:txt
AIzaSy extension:rst

# ==========================================
# 🎯 上下文语义搜索
# ==========================================

# Google API相关上下文
"google api key" AIzaSy
"gemini api" AIzaSy
"generative ai" AIzaSy
"google cloud" AIzaSy
"firebase" AIzaSy

# 常见变量名搜索
"api_key" AIzaSy
"apikey" AIzaSy
"API_KEY" AIzaSy
"GOOGLE_API_KEY" AIzaSy
"GEMINI_API_KEY" AIzaSy
"GENERATIVE_AI_KEY" AIzaSy

# 配置相关搜索
"config" AIzaSy
"settings" AIzaSy
"credentials" AIzaSy
"auth" AIzaSy

# ==========================================
# 🏗️ 项目类型定向搜索
# ==========================================

# AI/ML项目
AIzaSy "machine learning"
AIzaSy "artificial intelligence"
AIzaSy "chatbot"
AIzaSy "llm"
AIzaSy "gpt"

# Web开发项目
AIzaSy "react"
AIzaSy "vue"
AIzaSy "angular"
AIzaSy "next.js"
AIzaSy "express"

# 移动应用项目
AIzaSy "android"
AIzaSy "ios"
AIzaSy "flutter"
AIzaSy "react native"

# Python数据科学项目
AIzaSy "jupyter"
AIzaSy "pandas"
AIzaSy "tensorflow"
AIzaSy "pytorch"

# ==========================================
# 📊 特定文件名搜索
# ==========================================

# 常见配置文件名
AIzaSy filename:config.py
AIzaSy filename:settings.py
AIzaSy filename:config.js
AIzaSy filename:config.json
AIzaSy filename:.env.example
AIzaSy filename:.env.local
AIzaSy filename:credentials.json

# Docker和部署配置
AIzaSy filename:docker-compose.yml
AIzaSy filename:Dockerfile
AIzaSy filename:kubernetes.yaml

# CI/CD配置文件
AIzaSy filename:.github
AIzaSy filename:gitlab-ci.yml
AIzaSy filename:jenkins

# ==========================================
# 🔍 路径特定搜索
# ==========================================

# 配置目录
AIzaSy path:config
AIzaSy path:configs
AIzaSy path:settings
AIzaSy path:env

# 源码目录
AIzaSy path:src
AIzaSy path:lib
AIzaSy path:utils
AIzaSy path:api

# 测试目录（可能包含测试密钥）
AIzaSy path:test
AIzaSy path:tests
AIzaSy path:examples
AIzaSy path:demo

# ==========================================
# ⏰ 时间优化搜索（最近更新的项目）
# ==========================================

# 最近6个月更新的项目
AIzaSy pushed:>2024-07-01

# 最近1年更新的项目
AIzaSy pushed:>2024-01-01

# 高活跃度项目（星标数较多）
AIzaSy stars:>10
AIzaSy stars:>50
AIzaSy stars:>100

# ==========================================
# 🎲 组合搜索策略
# ==========================================

# 高价值组合：Python + 最近更新 + 配置文件
AIzaSy extension:py pushed:>2024-01-01
AIzaSy filename:config pushed:>2024-06-01
AIzaSy extension:env pushed:>2024-06-01

# AI项目组合搜索
AIzaSy "openai" extension:py
AIzaSy "langchain" extension:py
AIzaSy "streamlit" extension:py

# Web项目组合搜索
AIzaSy "next.js" extension:js
AIzaSy "react" extension:jsx
AIzaSy "vue" extension:vue

# 移动应用组合搜索
AIzaSy "flutter" extension:dart
AIzaSy "android" extension:java
AIzaSy "ios" extension:swift

# ==========================================
# 🔬 高级搜索技巧
# ==========================================

# 排除常见的示例和文档仓库
AIzaSy -path:example -path:demo -path:tutorial
AIzaSy -filename:README -filename:example

# 只搜索特定语言的仓库
AIzaSy language:Python
AIzaSy language:JavaScript
AIzaSy language:TypeScript
AIzaSy language:Java
AIzaSy language:Go

# 搜索特定组织的仓库（如果有目标）
# AIzaSy user:google
# AIzaSy org:tensorflow

# ==========================================
# 💡 创新搜索思路
# ==========================================

# 搜索错误信息中可能泄露的密钥
"Invalid API key" AIzaSy
"API key expired" AIzaSy
"Authentication failed" AIzaSy

# 搜索日志文件中的密钥
AIzaSy extension:log
AIzaSy "log" extension:txt

# 搜索备份文件中的密钥
AIzaSy extension:bak
AIzaSy extension:backup
AIzaSy filename:backup

# 搜索临时文件中的密钥
AIzaSy extension:tmp
AIzaSy filename:temp
