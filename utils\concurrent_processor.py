#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并发处理模块 - <PERSON><PERSON><PERSON> King 性能优化核心
提供异步并发处理能力，显著提升搜索和验证效率
"""

import asyncio
import aiohttp
import time
import random
import threading
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed
import google.generativeai as genai
from google.api_core import exceptions as google_exceptions

from common.Logger import logger
from common.config import Config


@dataclass
class ProcessingStats:
    """处理统计信息"""
    total_items: int = 0
    processed_items: int = 0
    valid_keys_found: int = 0
    rate_limited_keys: int = 0
    errors: int = 0
    start_time: float = 0
    
    def __post_init__(self):
        if self.start_time == 0:
            self.start_time = time.time()
    
    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time
    
    @property
    def progress_percentage(self) -> float:
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100
    
    @property
    def items_per_second(self) -> float:
        if self.elapsed_time == 0:
            return 0.0
        return self.processed_items / self.elapsed_time
    
    @property
    def eta_seconds(self) -> float:
        if self.items_per_second == 0:
            return 0.0
        remaining_items = self.total_items - self.processed_items
        return remaining_items / self.items_per_second


class ThreadSafeProgressTracker:
    """线程安全的进度跟踪器"""
    
    def __init__(self, total_items: int):
        self.stats = ProcessingStats(total_items=total_items)
        self._lock = threading.Lock()
        self._last_update = 0
        self._update_interval = 2  # 每2秒更新一次日志
    
    def update(self, processed: int = 1, valid_keys: int = 0, rate_limited: int = 0, errors: int = 0):
        """更新进度统计"""
        with self._lock:
            self.stats.processed_items += processed
            self.stats.valid_keys_found += valid_keys
            self.stats.rate_limited_keys += rate_limited
            self.stats.errors += errors
            
            # 定期输出进度日志
            current_time = time.time()
            if current_time - self._last_update >= self._update_interval:
                self._log_progress()
                self._last_update = current_time
    
    def _log_progress(self):
        """输出进度日志"""
        stats = self.stats
        logger.info(
            f"📊 Progress: {stats.processed_items}/{stats.total_items} "
            f"({stats.progress_percentage:.1f}%) | "
            f"Speed: {stats.items_per_second:.1f} items/s | "
            f"ETA: {stats.eta_seconds:.0f}s | "
            f"Found: {stats.valid_keys_found} valid, {stats.rate_limited_keys} rate-limited"
        )
    
    def get_stats(self) -> ProcessingStats:
        """获取当前统计信息"""
        with self._lock:
            return ProcessingStats(
                total_items=self.stats.total_items,
                processed_items=self.stats.processed_items,
                valid_keys_found=self.stats.valid_keys_found,
                rate_limited_keys=self.stats.rate_limited_keys,
                errors=self.stats.errors,
                start_time=self.stats.start_time
            )


class AsyncGitHubUtils:
    """异步版本的GitHub工具类"""
    
    def __init__(self, tokens: List[str]):
        self.tokens = [token.strip() for token in tokens if token.strip()]
        self._token_ptr = 0
        self._session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=20)
        timeout = aiohttp.ClientTimeout(total=30)
        self._session = aiohttp.ClientSession(connector=connector, timeout=timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self._session:
            await self._session.close()
    
    def _next_token(self) -> Optional[str]:
        """获取下一个GitHub Token"""
        if not self.tokens:
            return None
        token = self.tokens[self._token_ptr]
        self._token_ptr = (self._token_ptr + 1) % len(self.tokens)
        return token
    
    async def get_file_content_async(self, item: Dict[str, Any]) -> Optional[str]:
        """异步获取文件内容"""
        if not self._session:
            raise RuntimeError("AsyncGitHubUtils must be used as async context manager")
        
        repo_full_name = item["repository"]["full_name"]
        file_path = item["path"]
        
        metadata_url = f"https://api.github.com/repos/{repo_full_name}/contents/{file_path}"
        headers = {"Accept": "application/vnd.github.v3+json"}
        
        token = self._next_token()
        if token:
            headers["Authorization"] = f"token {token}"
        
        try:
            # 获取文件元数据
            async with self._session.get(metadata_url, headers=headers) as response:
                if response.status != 200:
                    logger.warning(f"⚠️ Failed to get metadata: {metadata_url}, status: {response.status}")
                    return None
                
                file_metadata = await response.json()
                download_url = file_metadata.get("download_url")
                
                if not download_url:
                    logger.warning(f"⚠️ No download URL found for file: {metadata_url}")
                    return None
            
            # 下载文件内容
            async with self._session.get(download_url, headers=headers) as response:
                if response.status != 200:
                    logger.warning(f"⚠️ Failed to download file: {download_url}, status: {response.status}")
                    return None
                
                content = await response.text()
                return content
                
        except asyncio.TimeoutError:
            logger.warning(f"⚠️ Timeout fetching file: {metadata_url}")
            return None
        except Exception as e:
            logger.error(f"❌ Error fetching file {metadata_url}: {type(e).__name__}: {e}")
            return None


class RateLimiter:
    """API速率限制管理器"""
    
    def __init__(self, max_concurrent: int = 5, min_delay: float = 0.5):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.min_delay = min_delay
        self._last_request_time = 0
        self._lock = asyncio.Lock()
    
    async def acquire(self):
        """获取API调用许可"""
        await self.semaphore.acquire()
        
        async with self._lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time
            
            if time_since_last < self.min_delay:
                sleep_time = self.min_delay - time_since_last
                await asyncio.sleep(sleep_time)
            
            self._last_request_time = time.time()
    
    def release(self):
        """释放API调用许可"""
        self.semaphore.release()


class ConcurrentProcessor:
    """并发处理协调器"""
    
    def __init__(self, max_file_workers: int = 10, max_validation_workers: int = 5):
        self.max_file_workers = max_file_workers
        self.max_validation_workers = max_validation_workers
        self.rate_limiter = RateLimiter(max_concurrent=max_validation_workers)
        
    async def process_items_concurrent(self, items: List[Dict[str, Any]], 
                                     extract_keys_func, validate_key_func, 
                                     file_manager, should_skip_func) -> Tuple[int, int]:
        """并发处理GitHub搜索结果items"""
        
        if not items:
            return 0, 0
        
        # 过滤需要跳过的items
        filtered_items = []
        for item in items:
            should_skip, reason = should_skip_func(item)
            if not should_skip:
                filtered_items.append(item)
        
        if not filtered_items:
            logger.info("📭 All items filtered out, skipping...")
            return 0, 0
        
        logger.info(f"🚀 Starting concurrent processing of {len(filtered_items)} items...")
        
        # 初始化进度跟踪器
        progress_tracker = ThreadSafeProgressTracker(len(filtered_items))
        
        # 使用异步GitHub工具
        async with AsyncGitHubUtils(Config.GITHUB_TOKENS) as github_utils:
            # 创建并发任务
            semaphore = asyncio.Semaphore(self.max_file_workers)
            tasks = [
                self._process_single_item(
                    item, github_utils, extract_keys_func, validate_key_func,
                    file_manager, progress_tracker, semaphore
                )
                for item in filtered_items
            ]
            
            # 执行并发任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        total_valid_keys = 0
        total_rate_limited_keys = 0
        
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"❌ Task failed: {result}")
                continue
            
            if isinstance(result, tuple) and len(result) == 2:
                valid_keys, rate_limited_keys = result
                total_valid_keys += valid_keys
                total_rate_limited_keys += rate_limited_keys
        
        # 输出最终统计
        final_stats = progress_tracker.get_stats()
        logger.info(
            f"🏁 Concurrent processing complete! "
            f"Processed: {final_stats.processed_items}/{final_stats.total_items} | "
            f"Time: {final_stats.elapsed_time:.1f}s | "
            f"Speed: {final_stats.items_per_second:.1f} items/s | "
            f"Found: {total_valid_keys} valid, {total_rate_limited_keys} rate-limited"
        )
        
        return total_valid_keys, total_rate_limited_keys
    
    async def _process_single_item(self, item: Dict[str, Any], github_utils: AsyncGitHubUtils,
                                 extract_keys_func, validate_key_func, file_manager,
                                 progress_tracker: ThreadSafeProgressTracker,
                                 semaphore: asyncio.Semaphore) -> Tuple[int, int]:
        """处理单个item"""
        
        async with semaphore:  # 限制并发数
            try:
                # 添加随机延迟，避免过于集中的请求
                await asyncio.sleep(random.uniform(0.1, 0.5))
                
                # 异步获取文件内容
                content = await github_utils.get_file_content_async(item)
                if not content:
                    progress_tracker.update(processed=1, errors=1)
                    return 0, 0
                
                # 提取密钥（CPU密集型，同步执行）
                keys = extract_keys_func(content)
                
                # 过滤占位符密钥
                filtered_keys = self._filter_placeholder_keys(keys, content)
                
                if not filtered_keys:
                    progress_tracker.update(processed=1)
                    return 0, 0
                
                # 并发验证密钥
                valid_keys, rate_limited_keys = await self._validate_keys_concurrent(
                    filtered_keys, validate_key_func
                )
                
                # 保存结果
                if valid_keys or rate_limited_keys:
                    repo_name = item["repository"]["full_name"]
                    file_path = item["path"]
                    file_url = item["html_url"]
                    
                    if valid_keys:
                        file_manager.save_valid_keys(repo_name, file_path, file_url, valid_keys)
                    
                    if rate_limited_keys:
                        file_manager.save_rate_limited_keys(repo_name, file_path, file_url, rate_limited_keys)
                
                # 更新进度
                progress_tracker.update(
                    processed=1,
                    valid_keys=len(valid_keys),
                    rate_limited=len(rate_limited_keys)
                )
                
                return len(valid_keys), len(rate_limited_keys)
                
            except Exception as e:
                logger.error(f"❌ Error processing item {item.get('html_url', 'unknown')}: {e}")
                progress_tracker.update(processed=1, errors=1)
                return 0, 0
    
    def _filter_placeholder_keys(self, keys: List[str], content: str) -> List[str]:
        """过滤占位符密钥"""
        filtered_keys = []
        for key in keys:
            context_index = content.find(key)
            if context_index != -1:
                snippet = content[context_index:context_index + 45]
                if "..." in snippet or "YOUR_" in snippet.upper():
                    continue
            filtered_keys.append(key)
        return filtered_keys
    
    async def _validate_keys_concurrent(self, keys: List[str], validate_key_func) -> Tuple[List[str], List[str]]:
        """并发验证密钥"""
        if not keys:
            return [], []
        
        # 创建验证任务
        tasks = [
            self._validate_single_key(key, validate_key_func)
            for key in keys
        ]
        
        # 执行并发验证
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        valid_keys = []
        rate_limited_keys = []
        
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"❌ Key validation failed: {keys[i]}, error: {result}")
                continue
            
            key = keys[i]
            if result == "ok":
                valid_keys.append(key)
                logger.info(f"✅ VALID: {key}")
            elif result == "rate_limited":
                rate_limited_keys.append(key)
                logger.warning(f"⚠️ RATE LIMITED: {key}")
            else:
                logger.info(f"❌ INVALID: {key}, result: {result}")
        
        return valid_keys, rate_limited_keys
    
    async def _validate_single_key(self, api_key: str, validate_key_func) -> str:
        """验证单个密钥"""
        await self.rate_limiter.acquire()
        try:
            # 在线程池中执行同步的验证函数
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=1) as executor:
                result = await loop.run_in_executor(executor, validate_key_func, api_key)
            return result
        finally:
            self.rate_limiter.release()


# 便捷函数
def create_concurrent_processor(max_file_workers: int = None, max_validation_workers: int = None) -> ConcurrentProcessor:
    """创建并发处理器实例"""
    
    # 根据系统配置自动调整默认值
    if max_file_workers is None:
        max_file_workers = min(20, (Config.GITHUB_TOKENS.__len__() or 1) * 5)
    
    if max_validation_workers is None:
        max_validation_workers = min(10, max_file_workers // 2)
    
    logger.info(f"🔧 Creating concurrent processor: {max_file_workers} file workers, {max_validation_workers} validation workers")
    
    return ConcurrentProcessor(
        max_file_workers=max_file_workers,
        max_validation_workers=max_validation_workers
    )
